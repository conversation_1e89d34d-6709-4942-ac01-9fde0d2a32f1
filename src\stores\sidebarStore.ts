import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export interface NavigationItem {
  id: string
  label: string
  href: string
  icon: string
  badge?: string | number
  children?: NavigationItem[]
}

export interface FooterItem {
  id: string
  label: string
  href: string
  icon: string
}

interface SidebarState {
  isOpen: boolean
  navigationItems: NavigationItem[]
  footerItems: FooterItem[]
  setIsOpen: (isOpen: boolean) => void
  toggleSidebar: () => void
  setNavigationItems: (items: NavigationItem[]) => void
  setFooterItems: (items: FooterItem[]) => void
}

const defaultNavigationItems: NavigationItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    href: '/',
    icon: 'icon-[solar--home-2-bold-duotone]',
  },
  {
    id: 'analytics',
    label: 'Analytics',
    href: '/analytics',
    icon: 'icon-[solar--chart-bold-duotone]',
    badge: 'New',
  },
  {
    id: 'projects',
    label: 'Projects',
    href: '/projects',
    icon: 'icon-[solar--folder-bold-duotone]',
  },
  {
    id: 'tasks',
    label: 'Tasks',
    href: '/tasks',
    icon: 'icon-[solar--checklist-bold-duotone]',
    badge: 12,
  },
  {
    id: 'team',
    label: 'Team',
    href: '/team',
    icon: 'icon-[solar--users-group-rounded-bold-duotone]',
  },
  {
    id: 'messages',
    label: 'Messages',
    href: '/messages',
    icon: 'icon-[solar--chat-round-dots-bold-duotone]',
    badge: 3,
  },
]

const defaultFooterItems: FooterItem[] = [
  {
    id: 'settings',
    label: 'Settings',
    href: '/settings',
    icon: 'icon-[solar--settings-bold-duotone]',
  },
  {
    id: 'help',
    label: 'Help & Support',
    href: '/help',
    icon: 'icon-[solar--question-circle-bold-duotone]',
  },
]

export const useSidebarStore = create<SidebarState>()(
  persist(
    (set, get) => ({
      isOpen: false,
      navigationItems: defaultNavigationItems,
      footerItems: defaultFooterItems,

      setIsOpen: (isOpen: boolean) => {
        set({ isOpen })
      },

      toggleSidebar: () => {
        const { isOpen } = get()
        set({ isOpen: !isOpen })
      },

      setNavigationItems: (items: NavigationItem[]) => {
        set({ navigationItems: items })
      },

      setFooterItems: (items: FooterItem[]) => {
        set({ footerItems: items })
      },
    }),
    {
      name: 'sidebar-storage',
      partialize: (state) => ({
        isOpen: state.isOpen,
        navigationItems: state.navigationItems,
        footerItems: state.footerItems,
      }),
    }
  )
)
